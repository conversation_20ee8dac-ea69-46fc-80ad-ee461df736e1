package com.chic.dea.apis.model.dto;

import com.alibaba.druid.util.JdbcConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据源类型枚举
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Getter
@AllArgsConstructor
public enum DataSourceType {
    
    MYSQL("MYSQL", "MySQL数据库", JdbcConstants.MYSQL_DRIVER_6, 3306),
    ORACLE("ORACLE", "Oracle数据库", JdbcConstants.ORACLE_DRIVER2, 1521),
    POSTGRESQL("POSTGRESQL", "PostgreSQL数据库", JdbcConstants.POSTGRESQL_DRIVER, 5432),
    ClickHouse("ClickHouse", "ClickHouse数据库", JdbcConstants.CLICKHOUSE_DRIVER, 8123);
    
    /**
     * 数据源类型代码
     */
    private final String code;
    
    /**
     * 数据源类型名称
     */
    private final String name;
    
    /**
     * 数据库驱动类名
     */
    private final String driverClassName;
    
    /**
     * 默认端口号
     */
    private final Integer defaultPort;
    
    /**
     * 根据代码获取数据源类型
     */
    public static DataSourceType fromCode(String code) {
        for (DataSourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的数据源类型: " + code);
    }
    
    /**
     * 构建JDBC URL
     */
    public String buildJdbcUrl(String host, Integer port, String databaseName) {
        port = port != null ? port : defaultPort;
        
        switch (this) {
            case MYSQL:
                return String.format("******************************************************************************************************", 
                    host, port, databaseName);
            case ORACLE:
                return String.format("**************************", host, port, databaseName);

            default:
                throw new UnsupportedOperationException("不支持的数据源类型: " + this.name);
        }
    }
}
