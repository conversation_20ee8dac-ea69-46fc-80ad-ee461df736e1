package com.chic.dea.domain.service;

import com.chic.dea.domain.database.entity.ExtractionTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批量数据提取使用示例
 * 展示如何使用Spring Batch + EasyExcel + 游标动态分文件大批量数据提取方案
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
public class BatchDataExtractionExample {
    
    @Autowired
    private EnhancedBatchJobService enhancedBatchJobService;
    
    /**
     * 示例1：标准数据提取
     * 适用于中等规模数据（100万条以下）
     */
    public void standardDataExtractionExample(ExtractionTask task) {
        log.info("=== 标准数据提取示例 ===");
        
        try {
            // 启动数据提取作业
            JobExecution jobExecution = enhancedBatchJobService.startExtraction(task);
            
            log.info("作业已启动，作业ID: {}", jobExecution.getId());
            
            // 监控作业执行进度
            monitorJobProgress(jobExecution, "标准数据提取");
            
            // 处理执行结果
            handleJobResult(jobExecution, "标准数据提取");
            
        } catch (Exception e) {
            log.error("标准数据提取失败", e);
        }
    }
    
    /**
     * 示例2：大数据集提取
     * 适用于大规模数据（500万条以上）
     */
    public void largeDatasetExtractionExample(ExtractionTask task) {
        log.info("=== 大数据集提取示例 ===");
        
        try {
            // 使用自定义配置：每文件10万条记录
            Integer maxRecordsPerFile = 100000;
            String customOutputPath = "/data/export/large_dataset";
            
            JobExecution jobExecution = enhancedBatchJobService.startExtraction(
                task, maxRecordsPerFile, customOutputPath);
            
            log.info("大数据集作业已启动，作业ID: {}", jobExecution.getId());
            log.info("自定义配置 - 每文件最大记录数: {}", maxRecordsPerFile);
            log.info("自定义输出路径: {}", customOutputPath);
            
            // 监控作业执行进度
            monitorJobProgress(jobExecution, "大数据集提取");
            
            // 处理执行结果
            handleJobResult(jobExecution, "大数据集提取");
            
        } catch (Exception e) {
            log.error("大数据集提取失败", e);
        }
    }
    
    /**
     * 示例3：简化数据提取
     * 适用于快速集成现有代码
     */
    public void simpleDataExtractionExample(String sql, Long dataSourceId, Long taskId) {
        log.info("=== 简化数据提取示例 ===");
        
        try {
            JobExecution jobExecution = enhancedBatchJobService.startExtractionSimple(
                sql, dataSourceId, taskId);
            
            log.info("简化作业已启动，作业ID: {}", jobExecution.getId());
            
            // 监控作业执行进度
            monitorJobProgress(jobExecution, "简化数据提取");
            
            // 处理执行结果
            handleJobResult(jobExecution, "简化数据提取");
            
        } catch (Exception e) {
            log.error("简化数据提取失败", e);
        }
    }
    
    /**
     * 示例4：批量处理多个任务
     */
    public void batchMultipleTasksExample(ExtractionTask[] tasks) {
        log.info("=== 批量处理多个任务示例 ===");
        
        for (int i = 0; i < tasks.length; i++) {
            ExtractionTask task = tasks[i];
            log.info("开始处理第 {} 个任务，任务ID: {}", i + 1, task.getId());
            
            try {
                JobExecution jobExecution = enhancedBatchJobService.startExtraction(task);
                
                // 等待当前任务完成再处理下一个
                while (!enhancedBatchJobService.isJobCompleted(jobExecution)) {
                    Thread.sleep(5000); // 每5秒检查一次
                    double progress = enhancedBatchJobService.getProgressPercentage(jobExecution);
                    log.info("任务 {} 进度: {:.2f}%", task.getId(), progress);
                }
                
                if (enhancedBatchJobService.isJobSuccessful(jobExecution)) {
                    log.info("任务 {} 完成成功", task.getId());
                } else {
                    log.error("任务 {} 执行失败", task.getId());
                }
                
            } catch (Exception e) {
                log.error("任务 {} 执行异常", task.getId(), e);
            }
        }
        
        log.info("批量处理完成，共处理 {} 个任务", tasks.length);
    }
    
    /**
     * 监控作业执行进度
     */
    private void monitorJobProgress(JobExecution jobExecution, String jobType) {
        log.info("开始监控 {} 作业执行进度", jobType);
        
        try {
            while (!enhancedBatchJobService.isJobCompleted(jobExecution)) {
                Thread.sleep(3000); // 每3秒检查一次
                
                double progress = enhancedBatchJobService.getProgressPercentage(jobExecution);
                BatchStatus status = enhancedBatchJobService.getJobStatus(jobExecution);
                
                log.info("{} - 状态: {}, 进度: {:.2f}%", jobType, status, progress);
                
                // 如果作业失败，提前退出监控
                if (status == BatchStatus.FAILED) {
                    log.error("{} 作业执行失败", jobType);
                    break;
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("{} 作业监控被中断", jobType);
        }
    }
    
    /**
     * 处理作业执行结果
     */
    private void handleJobResult(JobExecution jobExecution, String jobType) {
        if (enhancedBatchJobService.isJobSuccessful(jobExecution)) {
            log.info("=== {} 执行成功 ===", jobType);
            
            // 输出执行摘要
            String summary = enhancedBatchJobService.getJobExecutionSummary(jobExecution);
            log.info("执行摘要:\n{}", summary);
            
            // 输出性能统计
            logPerformanceStatistics(jobExecution);
            
        } else {
            log.error("=== {} 执行失败 ===", jobType);
            
            // 输出失败详情
            String summary = enhancedBatchJobService.getJobExecutionSummary(jobExecution);
            log.error("失败详情:\n{}", summary);
            
            // 输出错误信息
            jobExecution.getStepExecutions().forEach(stepExecution -> {
                if (!stepExecution.getFailureExceptions().isEmpty()) {
                    log.error("Step {} 异常:", stepExecution.getStepName());
                    stepExecution.getFailureExceptions().forEach(exception -> 
                        log.error("- {}", exception.getMessage()));
                }
            });
        }
    }
    
    /**
     * 输出性能统计信息
     */
    private void logPerformanceStatistics(JobExecution jobExecution) {
        long totalRecords = jobExecution.getStepExecutions().stream()
                .mapToLong(stepExecution -> stepExecution.getWriteCount())
                .sum();
        
        if (jobExecution.getStartTime() != null && jobExecution.getEndTime() != null) {
            long duration = jobExecution.getEndTime().getTime() - 
                           jobExecution.getStartTime().getTime();
            
            if (duration > 0) {
                double recordsPerSecond = (double) totalRecords * 1000 / duration;
                double recordsPerMinute = recordsPerSecond * 60;
                
                log.info("=== 性能统计 ===");
                log.info("总处理记录数: {}", totalRecords);
                log.info("总执行时间: {} ms ({} 秒)", duration, duration / 1000.0);
                log.info("处理速度: {:.2f} 记录/秒", recordsPerSecond);
                log.info("处理速度: {:.2f} 记录/分钟", recordsPerMinute);
                
                // 估算文件大小
                long estimatedFileSize = totalRecords * 100; // 假设每条记录100字节
                double fileSizeMB = estimatedFileSize / (1024.0 * 1024.0);
                log.info("估算文件大小: {:.2f} MB", fileSizeMB);
            }
        }
    }
    
    /**
     * 创建示例任务
     */
    public ExtractionTask createSampleTask(String oaid, String title, String sql, Long dataSourceId) {
        ExtractionTask task = new ExtractionTask();
        task.setOaid(oaid);
        task.setTitle(title);
        task.setExtractionScript(sql);
        task.setDataSourceId(dataSourceId);
        return task;
    }
    
    /**
     * 完整的使用示例
     */
    public void completeUsageExample() {
        log.info("=== 完整使用示例 ===");
        
        // 1. 创建示例任务
        ExtractionTask task = createSampleTask(
            "DEMO_001", 
            "用户数据提取示例", 
            "SELECT user_id, username, email, create_time FROM users WHERE status = 'ACTIVE'",
            1L
        );
        
        // 2. 设置预期记录数
        task.setTotalRecords(150000L); // 15万条记录
        
        // 3. 执行标准数据提取
        standardDataExtractionExample(task);
        
        log.info("完整使用示例执行完成");
    }
}
