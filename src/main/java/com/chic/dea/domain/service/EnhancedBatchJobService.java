package com.chic.dea.domain.service;

import com.chic.dea.domain.batch.CursorItemReader;
import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.database.entity.ExtractionTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 增强的批处理作业服务
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
public class EnhancedBatchJobService {
    
    @Autowired
    private JobLauncher jobLauncher;
    
    @Autowired
    private Job dynamicDataExtractionJob;
    
    @Autowired
    private BatchExportConfig config;
    
    @Autowired
    private CursorItemReader cursorItemReader;
    
    /**
     * 启动数据提取作业
     * 
     * @param task 提取任务
     * @param maxRecordsPerFile 每个文件最大记录数（可覆盖配置）
     * @param outputPath 输出路径（可选）
     * @return 作业执行结果
     */
    public JobExecution startExtraction(ExtractionTask task, Integer maxRecordsPerFile, String outputPath) throws Exception {
        
        // 初始化读取器
        cursorItemReader.initialize(task.getExtractionScript(), task.getDataSourceId());
        
        JobParametersBuilder builder = new JobParametersBuilder()
                .addLong("timestamp", System.currentTimeMillis())
                .addLong("taskId", task.getId())
                .addString("oaid", task.getOaid())
                .addLong("dataSourceId", task.getDataSourceId())
                .addLong("maxRecordsPerFile",
                        maxRecordsPerFile != null ? maxRecordsPerFile : config.getMaxRecordsPerFile());
        
        if (StringUtils.hasText(outputPath)) {
            builder.addString("outputPath", outputPath);
        }
        
        if (StringUtils.hasText(task.getTitle())) {
            builder.addString("taskTitle", task.getTitle());
        }
        
        JobParameters parameters = builder.toJobParameters();
        
        log.info("启动数据提取作业");
        log.info("- 任务ID: {}", task.getId());
        log.info("- OA ID: {}", task.getOaid());
        log.info("- 任务标题: {}", task.getTitle());
        log.info("- 数据源ID: {}", task.getDataSourceId());
        log.info("- 每文件最大记录数: {}", parameters.getLong("maxRecordsPerFile"));
        log.info("- Chunk大小: {}", config.getChunkSize());
        log.info("- Fetch大小: {}", config.getFetchSize());
        
        return jobLauncher.run(dynamicDataExtractionJob, parameters);
    }
    
    /**
     * 使用默认配置启动
     */
    public JobExecution startExtraction(ExtractionTask task) throws Exception {
        return startExtraction(task, null, null);
    }
    
    /**
     * 启动数据提取作业（简化版本，用于兼容现有代码）
     */
    public JobExecution startExtractionSimple(String sql, Long dataSourceId, Long taskId) throws Exception {
        
        // 初始化读取器
        cursorItemReader.initialize(sql, dataSourceId);
        
        JobParametersBuilder builder = new JobParametersBuilder()
                .addLong("timestamp", System.currentTimeMillis())
                .addLong("taskId", taskId)
                .addLong("dataSourceId", dataSourceId)
                .addLong("maxRecordsPerFile", config.getMaxRecordsPerFile());
        
        JobParameters parameters = builder.toJobParameters();
        
        log.info("启动简化数据提取作业");
        log.info("- 任务ID: {}", taskId);
        log.info("- 数据源ID: {}", dataSourceId);
        log.info("- 每文件最大记录数: {}", config.getMaxRecordsPerFile());
        
        return jobLauncher.run(dynamicDataExtractionJob, parameters);
    }
    
    /**
     * 检查作业执行状态
     */
    public BatchStatus getJobStatus(JobExecution jobExecution) {
        if (jobExecution == null) {
            return BatchStatus.UNKNOWN;
        }
        return jobExecution.getStatus();
    }
    
    /**
     * 检查作业是否完成
     */
    public boolean isJobCompleted(JobExecution jobExecution) {
        return jobExecution != null && 
               (jobExecution.getStatus() == BatchStatus.COMPLETED || 
                jobExecution.getStatus() == BatchStatus.FAILED ||
                jobExecution.getStatus() == BatchStatus.STOPPED);
    }
    
    /**
     * 检查作业是否成功
     */
    public boolean isJobSuccessful(JobExecution jobExecution) {
        return jobExecution != null && jobExecution.getStatus() == BatchStatus.COMPLETED;
    }
    
    /**
     * 获取作业执行摘要
     */
    public String getJobExecutionSummary(JobExecution jobExecution) {
        if (jobExecution == null) {
            return "作业执行信息不可用";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("作业执行摘要:\n");
        summary.append("- 作业ID: ").append(jobExecution.getId()).append("\n");
        summary.append("- 作业状态: ").append(jobExecution.getStatus()).append("\n");
        summary.append("- 开始时间: ").append(jobExecution.getStartTime()).append("\n");
        summary.append("- 结束时间: ").append(jobExecution.getEndTime()).append("\n");
        
        if (jobExecution.getStartTime() != null && jobExecution.getEndTime() != null) {
            long duration = jobExecution.getEndTime().getTime() - jobExecution.getStartTime().getTime();
            summary.append("- 执行时长: ").append(duration).append(" ms\n");
        }
        
        // Step执行信息
        for (StepExecution stepExecution : jobExecution.getStepExecutions()) {
            summary.append("- Step: ").append(stepExecution.getStepName()).append("\n");
            summary.append("  - 读取: ").append(stepExecution.getReadCount()).append(" 条\n");
            summary.append("  - 写入: ").append(stepExecution.getWriteCount()).append(" 条\n");
            summary.append("  - 跳过: ").append(stepExecution.getSkipCount()).append(" 条\n");
            
            if (stepExecution.getFailureExceptions() != null && !stepExecution.getFailureExceptions().isEmpty()) {
                summary.append("  - 异常: ").append(stepExecution.getFailureExceptions().size()).append(" 个\n");
            }
        }
        
        return summary.toString();
    }
    
    /**
     * 获取处理进度百分比
     */
    public double getProgressPercentage(JobExecution jobExecution) {
        if (jobExecution == null || jobExecution.getStepExecutions().isEmpty()) {
            return 0.0;
        }
        
        // 简单的进度计算，基于Step状态
        for (StepExecution stepExecution : jobExecution.getStepExecutions()) {
            if (stepExecution.getStatus() == BatchStatus.COMPLETED) {
                return 100.0;
            } else if (stepExecution.getStatus() == BatchStatus.STARTED) {
                // 基于读取和写入的记录数估算进度
                long readCount = stepExecution.getReadCount();
                long writeCount = stepExecution.getWriteCount();
                
                if (readCount > 0) {
                    return Math.min(95.0, (double) writeCount / readCount * 100);
                }
                return 10.0; // 已开始但无法精确计算
            }
        }
        
        return 0.0;
    }
}
