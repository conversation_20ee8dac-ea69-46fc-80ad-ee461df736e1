package com.chic.dea.domain.service;

import com.chic.dea.domain.database.entity.*;
import com.chic.dea.domain.database.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 任务调度器
 * 负责轮询和执行提数任务
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component("extractionTaskScheduler")
@EnableScheduling
@RequiredArgsConstructor
public class ExtractionTaskScheduler {

    private final TaskExecutionQueueMapper queueMapper;
    private final ExtractionTaskMapper taskMapper;
    private final TaskExecutionLogMapper logMapper;
    private final TaskExecutionService taskExecutionService;
    
    // 任务执行线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 轮询待执行任务（每5秒执行一次）
     */
    //@Scheduled(fixedDelay = 5000)
    public void pollPendingTasks() {
        try {
            log.debug("开始轮询待执行任务");
            
            LocalDateTime currentTime = LocalDateTime.now();
            List<TaskExecutionQueue> pendingQueue = queueMapper.findPendingQueue(currentTime);
            
            if (pendingQueue.isEmpty()) {
                log.debug("当前没有待执行的任务");
                return;
            }
            
            log.info("发现 {} 个待执行任务", pendingQueue.size());
            
            for (TaskExecutionQueue queueItem : pendingQueue) {
                // 异步执行任务
                CompletableFuture.runAsync(() -> executeTask(queueItem), executorService);
            }
            
        } catch (Exception e) {
            log.error("轮询待执行任务时发生异常", e);
        }
    }

    /**
     * 轮询失败重试任务（每1分钟执行一次）
     */
    //@Scheduled(fixedDelay = 60000)
    public void pollRetryTasks() {
        try {
            log.debug("开始轮询失败重试任务");
            
            LocalDateTime currentTime = LocalDateTime.now();
            List<TaskExecutionQueue> retryQueue = queueMapper.findRetryableQueue(currentTime);
            
            if (retryQueue.isEmpty()) {
                log.debug("当前没有需要重试的任务");
                return;
            }
            
            log.info("发现 {} 个需要重试的任务", retryQueue.size());
            
            for (TaskExecutionQueue queueItem : retryQueue) {
                // 更新重试信息
                queueItem.incrementRetryCount();
                queueItem.setNextExecuteTime(queueItem.calculateNextRetryTime());
                queueItem.setQueueStatusEnum(QueueStatus.PENDING);
                
                queueMapper.updateRetryInfo(
                    queueItem.getId(),
                    queueItem.getRetryCount(),
                    queueItem.getNextExecuteTime(),
                    QueueStatus.PENDING.name()
                );
                
                log.info("任务 {} 已设置为重试, 重试次数: {}, 下次执行时间: {}", 
                         queueItem.getTaskId(), queueItem.getRetryCount(), queueItem.getNextExecuteTime());
            }
            
        } catch (Exception e) {
            log.error("轮询失败重试任务时发生异常", e);
        }
    }

    /**
     * 清理超时的处理中任务（每10分钟执行一次）
     */
    //@Scheduled(fixedDelay = 600000)
    public void cleanupTimeoutTasks() {
        try {
            log.debug("开始清理超时的处理中任务");
            
            // 超过2小时未更新状态的任务视为超时
            LocalDateTime timeoutTime = LocalDateTime.now().minusHours(2);
            
            List<TaskExecutionQueue> timeoutQueue = queueMapper.findTimeoutProcessingQueue(timeoutTime);
            
            if (timeoutQueue.isEmpty()) {
                log.debug("当前没有超时的处理中任务");
                return;
            }
            
            log.warn("发现 {} 个超时的处理中任务", timeoutQueue.size());
            
            // 重置超时任务状态
            queueMapper.resetTimeoutQueue(timeoutTime);
            
            // 更新对应的任务状态
            for (TaskExecutionQueue queueItem : timeoutQueue) {
                taskMapper.updateErrorMessage(queueItem.getTaskId(), "任务执行超时，已重置为待执行状态");
                
                // 记录超时日志
                TaskExecutionLog timeoutLog = TaskExecutionLog.createFailedLog(
                    queueItem.getTaskId(),
                    "TIMEOUT_CLEANUP",
                    LocalDateTime.now().minusHours(2),
                    "任务执行超时，系统自动重置"
                );
                logMapper.insert(timeoutLog);
                
                log.warn("任务 {} 执行超时已重置", queueItem.getTaskId());
            }
            
        } catch (Exception e) {
            log.error("清理超时任务时发生异常", e);
        }
    }

    /**
     * 定期清理历史记录（每天凌晨2点执行）
     */
    //@Scheduled(cron = "0 0 2 * * ?")
    public void cleanupHistoryRecords() {
        try {
            log.info("开始清理历史记录");
            
            // 清理30天前的队列记录
            LocalDateTime queueCutoffTime = LocalDateTime.now().minusDays(30);
            queueMapper.cleanupOldRecords(queueCutoffTime);
            
            // 清理90天前的执行日志
            LocalDateTime logCutoffTime = LocalDateTime.now().minusDays(90);
            logMapper.cleanupOldLogs(logCutoffTime);
            
            log.info("历史记录清理完成");
            
        } catch (Exception e) {
            log.error("清理历史记录时发生异常", e);
        }
    }

    /**
     * 执行单个任务
     */
    @Transactional
    public void executeTask(TaskExecutionQueue queueItem) {
        Long taskId = queueItem.getTaskId();
        
        try {
            log.info("开始执行任务, taskId: {}", taskId);
            
            // 更新队列状态为处理中
            queueMapper.updateStatus(queueItem.getId(), QueueStatus.PROCESSING.name());
            
            // 更新任务执行开始时间
            taskMapper.updateExecutionStartTime(taskId, LocalDateTime.now());
            
            // 记录开始执行日志
            TaskExecutionLog startLog = new TaskExecutionLog();
            startLog.setTaskId(taskId);
            startLog.setStepName("TASK_START");
            startLog.setStatus("SUCCESS");
            startLog.setStartTime(LocalDateTime.now());
            startLog.setEndTime(LocalDateTime.now());
            logMapper.insert(startLog);
            
            // 执行具体的提数任务
            taskExecutionService.executeExtractionTask(taskId);
            
            // 执行成功，更新队列状态
            queueMapper.updateStatus(queueItem.getId(), QueueStatus.COMPLETED.name());
            
            log.info("任务执行成功, taskId: {}", taskId);
            
        } catch (Exception e) {
            log.error("任务执行失败, taskId: {}", taskId, e);
            handleTaskFailure(queueItem, e);
        }
    }

    /**
     * 处理任务执行失败
     */
    private void handleTaskFailure(TaskExecutionQueue queueItem, Exception e) {
        Long taskId = queueItem.getTaskId();
        
        try {
            // 记录失败日志
            TaskExecutionLog failureLog = TaskExecutionLog.createFailedLog(
                taskId,
                "TASK_EXECUTION",
                LocalDateTime.now(),
                "任务执行失败: " + e.getMessage()
            );
            logMapper.insert(failureLog);
            
            // 更新任务错误信息
            taskMapper.updateErrorMessage(taskId, e.getMessage());
            
            // 检查是否可以重试
            queueItem.incrementRetryCount();
            
            if (queueItem.canRetry()) {
                // 设置重试
                queueItem.setQueueStatusEnum(QueueStatus.PENDING);
                queueItem.setNextExecuteTime(queueItem.calculateNextRetryTime());
                
                queueMapper.updateRetryInfo(
                    queueItem.getId(),
                    queueItem.getRetryCount(),
                    queueItem.getNextExecuteTime(),
                    QueueStatus.PENDING.name()
                );
                
                log.info("任务 {} 将进行第 {} 次重试, 下次执行时间: {}", 
                         taskId, queueItem.getRetryCount(), queueItem.getNextExecuteTime());
            } else {
                // 达到最大重试次数，标记为失败
                queueMapper.updateStatus(queueItem.getId(), QueueStatus.FAILED.name());
                
                log.error("任务 {} 达到最大重试次数，标记为失败", taskId);
            }
            
        } catch (Exception ex) {
            log.error("处理任务失败时发生异常, taskId: {}", taskId, ex);
        }
    }

    /**
     * 优雅关闭调度器
     */
    public void shutdown() {
        log.info("开始关闭任务调度器");
        
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
                log.warn("强制关闭任务执行线程池");
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        log.info("任务调度器已关闭");
    }
}
