package com.chic.dea.domain.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chic.dea.domain.database.entity.DataSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据源Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Mapper
public interface DataSourceMapper extends BaseMapper<DataSource> {

    /**
     * 查询启用状态的数据源列表
     * 
     * @return 启用的数据源列表
     */
    @Select("SELECT * FROM data_source WHERE status = 1 ORDER BY update_time DESC")
    List<DataSource> findEnabledDataSources();

    /**
     * 根据名称查询数据源
     * 
     * @param name 数据源名称
     * @return 数据源实体
     */
    @Select("SELECT * FROM data_source WHERE name = #{name} LIMIT 1")
    DataSource findByName(@Param("name") String name);

    /**
     * 根据类型查询数据源列表
     * 
     * @param type 数据源类型
     * @return 数据源列表
     */
    @Select("SELECT * FROM data_source WHERE type = #{type} AND status = 1 ORDER BY update_time DESC")
    List<DataSource> findByType(@Param("type") String type);

    /**
     * 检查数据源名称是否存在
     * 
     * @param name 数据源名称
     * @param excludeId 排除的ID(用于更新时检查)
     * @return 存在数量
     */
    @Select("SELECT COUNT(1) FROM data_source WHERE name = #{name} AND id != #{excludeId}")
    int countByNameExcludeId(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 检查数据源名称是否存在(用于新增时检查)
     * 
     * @param name 数据源名称
     * @return 存在数量
     */
    @Select("SELECT COUNT(1) FROM data_source WHERE name = #{name}")
    int countByName(@Param("name") String name);
}
