package com.chic.dea.domain.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 批量导出配置类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@ConfigurationProperties(prefix = "batch.export")
@Component
@Data
public class BatchExportConfig {
    
    /**
     * 每个CSV文件的最大记录数（可配置）
     */
    private int maxRecordsPerFile = 100000;
    
    /**
     * 输出文件基础路径
     */
    private String outputBasePath = "/data/export";
    
    /**
     * 文件名前缀
     */
    private String filePrefix = "data_export";
    
    /**
     * chunk处理大小
     */
    private int chunkSize = 2000;
    
    /**
     * 游标fetch大小
     */
    private int fetchSize = 1000;
    
    /**
     * 并行写入线程数
     */
    private int writeThreads = 3;
    
    /**
     * 内存使用率阈值
     */
    private double memoryThreshold = 0.8;
    
    /**
     * 是否启用异步写入
     */
    private boolean enableAsyncWrite = true;
    
    /**
     * 是否启用文件压缩
     */
    private boolean enableCompression = true;
    
    /**
     * 缓冲区刷新阈值
     */
    private int bufferFlushThreshold = 1000;
}
