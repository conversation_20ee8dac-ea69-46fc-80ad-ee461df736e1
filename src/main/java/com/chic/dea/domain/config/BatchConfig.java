package com.chic.dea.domain.config;

import com.chic.dea.domain.batch.*;
import com.chic.dea.domain.model.CsvOutputModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.JobRepositoryFactoryBean;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.support.DatabaseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Spring Batch配置类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Configuration
@EnableBatchProcessing
public class BatchConfig {

    @Autowired
    private BatchExportConfig config;

    @Autowired
    private CursorItemReader cursorItemReader;

    @Autowired
    private DataProcessor dataProcessor;

    @Autowired
    private EnhancedDynamicCsvWriter enhancedDynamicCsvWriter;

    @Autowired
    private FileStatisticsListener fileStatisticsListener;
    
    /**
     * 动态数据提取Step
     */
    @Bean
    public Step dynamicExportStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return new StepBuilder("dynamicExportStep")
                .repository(jobRepository)
                .<CsvOutputModel, CsvOutputModel>chunk(config.getChunkSize())
                .transactionManager(transactionManager)
                .reader(cursorItemReader)
                .processor(dataProcessor)
                .writer(enhancedDynamicCsvWriter)
                .listener(fileStatisticsListener)
                .faultTolerant()
                .skipLimit(1000)
                .skip(Exception.class)
                .retryLimit(3)
                .retry(TransientDataAccessException.class)
                .build();
    }
    
    /**
     * 动态数据提取Job
     */
    @Bean
    public Job dynamicDataExtractionJob(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return new JobBuilder("dynamicDataExtractionJob")
                .repository(jobRepository)
                .incrementer(new RunIdIncrementer())
                .start(dynamicExportStep(jobRepository, transactionManager))
                .listener(fileStatisticsListener)
                .build();
    }
    
    /**
     * 自定义JobRepository配置
     * 使用现有数据源
     */
    @Bean
    public JobRepository jobRepository(DataSource dataSource, 
                                     PlatformTransactionManager transactionManager) throws Exception {
        JobRepositoryFactoryBean factory = new JobRepositoryFactoryBean();
        factory.setDataSource(dataSource);
        factory.setTransactionManager(transactionManager);
        factory.setDatabaseType(DatabaseType.MYSQL.getProductName());
        factory.setTablePrefix("BATCH_");
        factory.setIsolationLevelForCreate("ISOLATION_READ_COMMITTED");
        factory.afterPropertiesSet();
        return factory.getObject();
    }
}
