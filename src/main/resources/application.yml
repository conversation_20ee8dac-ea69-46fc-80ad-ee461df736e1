
server:
  servlet:
    context-path: /data-extract-audit
  port: 8248
  shutdown: graceful

spring:
  application:
    name: '@project.name@'
    version: '@project.version@'
    branch: '@scmBranch@'
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  profiles:
    active: dev
  batch:
    job:
      enabled: false  # 禁用自动启动批处理作业
    jdbc:
      initialize-schema: always  # 自动创建Spring Batch表
#  boot:
#    admin:
#      client:
#        url: http://${ci.environment.slug}-monitor.chinahuanong.com.cn/monitor
#        instance:
#          name: ${spring.application.name}
#          prefer-ip: true

# 批量导出配置
batch:
  export:
    max-records-per-file: 50000    # 每个文件5万条记录
    output-base-path: /data/export
    file-prefix: data_export
    chunk-size: 2000
    fetch-size: 1000
    write-threads: 3
    memory-threshold: 0.8
    enable-async-write: true
    enable-compression: true
    buffer-flush-threshold: 1000
