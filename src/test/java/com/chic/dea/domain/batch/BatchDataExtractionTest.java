package com.chic.dea.domain.batch;

import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.database.entity.ExtractionTask;
import com.chic.dea.domain.service.EnhancedBatchJobService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 批量数据提取测试类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class BatchDataExtractionTest {
    
    @Autowired
    private EnhancedBatchJobService enhancedBatchJobService;
    
    @Autowired
    private BatchExportConfig batchExportConfig;
    
    @Autowired
    private CursorItemReader cursorItemReader;
    
    @Autowired
    private EnhancedDynamicCsvWriter enhancedDynamicCsvWriter;
    
    @Test
    public void testBatchConfigurationLoaded() {
        assertNotNull(batchExportConfig);
        assertTrue(batchExportConfig.getMaxRecordsPerFile() > 0);
        assertTrue(batchExportConfig.getChunkSize() > 0);
        assertTrue(batchExportConfig.getFetchSize() > 0);
        
        log.info("批量配置测试通过:");
        log.info("- 每文件最大记录数: {}", batchExportConfig.getMaxRecordsPerFile());
        log.info("- Chunk大小: {}", batchExportConfig.getChunkSize());
        log.info("- Fetch大小: {}", batchExportConfig.getFetchSize());
    }
    
    @Test
    public void testBatchJobServiceInitialization() {
        assertNotNull(enhancedBatchJobService);
        log.info("批量作业服务初始化测试通过");
    }
    
    @Test
    public void testCursorItemReaderInitialization() {
        assertNotNull(cursorItemReader);
        
        // 测试读取器初始化
        String testSql = "SELECT 1 as id, 'test' as name";
        Long testDataSourceId = 1L;
        
        assertDoesNotThrow(() -> {
            cursorItemReader.initialize(testSql, testDataSourceId);
        });
        
        log.info("游标读取器初始化测试通过");
    }
    
    @Test
    public void testEnhancedDynamicCsvWriterInitialization() {
        assertNotNull(enhancedDynamicCsvWriter);
        
        // 测试写入器重置
        assertDoesNotThrow(() -> {
            enhancedDynamicCsvWriter.reset();
        });
        
        log.info("增强版动态CSV写入器初始化测试通过");
    }
    
    /**
     * 测试小数据集批处理
     * 注意：这个测试需要真实的数据库连接，在实际环境中运行
     */
    // @Test
    public void testSmallDatasetBatchProcessing() {
        // 创建测试任务
        ExtractionTask testTask = createTestTask();
        testTask.setTotalRecords(1000L); // 小数据集
        
        try {
            JobExecution jobExecution = enhancedBatchJobService.startExtraction(testTask);
            
            // 等待作业完成
            while (!enhancedBatchJobService.isJobCompleted(jobExecution)) {
                Thread.sleep(1000);
                log.info("作业执行中，状态: {}", jobExecution.getStatus());
            }
            
            // 验证作业执行结果
            assertTrue(enhancedBatchJobService.isJobSuccessful(jobExecution));
            assertEquals(BatchStatus.COMPLETED, jobExecution.getStatus());
            
            log.info("小数据集批处理测试通过");
            log.info("作业摘要: {}", enhancedBatchJobService.getJobExecutionSummary(jobExecution));
            
        } catch (Exception e) {
            log.error("小数据集批处理测试失败", e);
            fail("批处理测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试大数据集批处理
     * 注意：这个测试需要真实的数据库连接，在实际环境中运行
     */
    // @Test
    public void testLargeDatasetBatchProcessing() {
        // 创建测试任务
        ExtractionTask testTask = createTestTask();
        testTask.setTotalRecords(6000000L); // 大数据集，超过500万
        
        try {
            // 使用自定义配置
            Integer maxRecordsPerFile = 100000; // 每文件10万条
            JobExecution jobExecution = enhancedBatchJobService.startExtraction(
                testTask, maxRecordsPerFile, null);
            
            // 监控作业执行
            while (!enhancedBatchJobService.isJobCompleted(jobExecution)) {
                Thread.sleep(2000);
                double progress = enhancedBatchJobService.getProgressPercentage(jobExecution);
                log.info("大数据集处理进度: {:.2f}%", progress);
            }
            
            // 验证作业执行结果
            assertTrue(enhancedBatchJobService.isJobSuccessful(jobExecution));
            assertEquals(BatchStatus.COMPLETED, jobExecution.getStatus());
            
            log.info("大数据集批处理测试通过");
            log.info("作业摘要: {}", enhancedBatchJobService.getJobExecutionSummary(jobExecution));
            
        } catch (Exception e) {
            log.error("大数据集批处理测试失败", e);
            fail("大数据集批处理测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试动态分文件功能
     */
    @Test
    public void testDynamicFileManagement() {
        // 这里可以测试文件管理器的逻辑
        log.info("动态分文件管理测试 - 需要在集成测试中验证实际文件生成");
    }
    
    /**
     * 测试内存优化功能
     */
    @Test
    public void testMemoryOptimization() {
        // 这里可以测试内存监控和优化逻辑
        log.info("内存优化功能测试 - 需要在高负载环境中验证");
    }
    
    /**
     * 测试并发写入功能
     */
    @Test
    public void testConcurrentWriting() {
        // 这里可以测试并发写入的性能和正确性
        log.info("并发写入功能测试 - 需要在多线程环境中验证");
    }
    
    /**
     * 创建测试任务
     */
    private ExtractionTask createTestTask() {
        ExtractionTask task = new ExtractionTask();
        task.setId(999L);
        task.setOaid("TEST_OA_001");
        task.setTitle("批量数据提取测试任务");
        task.setDataSourceId(1L);
        task.setExtractionScript("SELECT * FROM test_table LIMIT 1000");
        return task;
    }
    
    /**
     * 性能基准测试
     */
    // @Test
    public void performanceBenchmarkTest() {
        log.info("=== 性能基准测试 ===");
        
        // 测试不同数据量的处理性能
        int[] testSizes = {1000, 10000, 100000, 500000};
        
        for (int size : testSizes) {
            long startTime = System.currentTimeMillis();
            
            // 模拟数据处理
            try {
                Thread.sleep(size / 1000); // 模拟处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            double recordsPerSecond = size * 1000.0 / duration;
            
            log.info("数据量: {} 条, 处理时间: {} ms, 处理速度: {:.2f} 条/秒", 
                    size, duration, recordsPerSecond);
        }
    }
    
    /**
     * 配置验证测试
     */
    @Test
    public void testConfigurationValidation() {
        // 验证配置的合理性
        assertTrue(batchExportConfig.getMaxRecordsPerFile() >= 1000, 
                "每文件最大记录数应该至少为1000");
        assertTrue(batchExportConfig.getChunkSize() >= 100, 
                "Chunk大小应该至少为100");
        assertTrue(batchExportConfig.getFetchSize() >= 100, 
                "Fetch大小应该至少为100");
        assertTrue(batchExportConfig.getBufferFlushThreshold() >= 100, 
                "缓冲区刷新阈值应该至少为100");
        
        log.info("配置验证测试通过");
    }
}
